# FDN（Funeral Director Navi）システム概要資料
## 新規開発者向けオンボーディングガイド

---

## 📋 目次

1. [システム概要](#1-システム概要)
2. [技術スタック](#2-技術スタック)
3. [アーキテクチャ](#3-アーキテクチャ)
4. [ディレクトリ構造](#4-ディレクトリ構造)
5. [カスタマイズキーシステム](#5-カスタマイズキーシステム)
6. [主要モジュール](#6-主要モジュール)
7. [データベース設計](#7-データベース設計)
8. [フロントエンド技術](#8-フロントエンド技術)
9. [セキュリティ・認証](#9-セキュリティ認証)
10. [開発環境セットアップ](#10-開発環境セットアップ)

---

## 1. システム概要

### 1.1 FDNとは
**FDN（Funeral Director Navi）** は、葬儀社向けの総合施行管理システムです。

### 1.2 主要機能
- **顧客情報管理**: 故人・遺族情報の管理
- **受注管理**: 葬儀の受注から施行まで
- **発注管理**: 必要な商品・サービスの発注
- **請求管理**: 料金計算・請求書発行
- **帳票出力**: 各種帳票・見積書の出力
- **マスタ管理**: 商品・会社・ユーザー管理

### 1.3 システムの特徴
- **マルチテナント対応**: 複数の葬儀社が同一システムを利用
- **カスタマイズ対応**: 会社ごとの業務要件に対応
- **Web ベース**: ブラウザからアクセス可能

---

## 2. 技術スタック

### 2.1 サーバーサイド
| 技術 | バージョン | 用途 |
|------|------------|------|
| **PHP** | 7.x | サーバーサイド言語 |
| **Zend Framework** | 1.x | MVCフレームワーク |
| **PostgreSQL** | 9.x+ | データベース |
| **Apache** | 2.x | Webサーバー |
| **Smarty** | 3.x | テンプレートエンジン |

### 2.2 フロントエンド
| 技術 | バージョン | 用途 |
|------|------------|------|
| **jQuery** | 1.10.2 | DOM操作・Ajax |
| **jQuery UI** | 1.10.3 | UI コンポーネント |
| **Backbone.js** | 1.1.0 | MVCフレームワーク |
| **Underscore.js** | 1.5.2 | ユーティリティライブラリ |
| **RequireJS** | - | モジュール管理 |

### 2.3 その他ライブラリ
- **PHPExcel**: Excel ファイル操作
- **TCPDF**: PDF 生成
- **php-activerecord**: ORM
- **SlickGrid**: データグリッド表示

---

## 3. アーキテクチャ

### 3.1 全体アーキテクチャ
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   ブラウザ      │    │   Webサーバー   │    │  データベース   │
│                 │    │                 │    │                 │
│ HTML/CSS/JS     │◄──►│ Apache + PHP    │◄──►│ PostgreSQL      │
│ jQuery/Backbone │    │ Zend Framework  │    │ マルチDB対応    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 MVC パターン
- **Model**: DataMapper、Logic、ActiveRecord
- **View**: Smarty テンプレート + JavaScript
- **Controller**: Zend Framework コントローラ

### 3.3 レイヤー構成
```
┌─────────────────────────────────────────┐
│ Presentation Layer (Smarty + JS)       │
├─────────────────────────────────────────┤
│ Controller Layer (Zend Controllers)    │
├─────────────────────────────────────────┤
│ Business Logic Layer (Logic_*)         │
├─────────────────────────────────────────┤
│ Data Access Layer (DataMapper_*)       │
├─────────────────────────────────────────┤
│ Database Layer (PostgreSQL)            │
└─────────────────────────────────────────┘
```

---

## 4. ディレクトリ構造

### 4.1 プロジェクト全体
```
Fdn.keigen/
├── application/           # アプリケーション本体
│   ├── configs/          # 設定ファイル
│   ├── controllers/      # 共通コントローラ
│   ├── library/          # アプリケーション固有ライブラリ
│   ├── models/           # モデル（DataMapper、Logic等）
│   ├── modules/          # 機能モジュール
│   │   ├── juchu/       # 受注モジュール
│   │   ├── hachu/       # 発注モジュール
│   │   ├── seikyu/      # 請求モジュール
│   │   ├── mref/        # 参照モジュール
│   │   ├── mstr/        # マスタ管理モジュール
│   │   └── kanri/       # 管理モジュール
│   └── views/
│       └── smarty/      # Smartyテンプレート
├── library/              # 共通ライブラリ
│   ├── Msi/             # MSI独自ライブラリ
│   ├── Zend/            # Zend Framework
│   └── php-activerecord/ # ActiveRecord ORM
├── public_dev/           # 開発用公開ファイル
│   ├── css/             # CSSファイル
│   ├── js/              # JavaScriptファイル
│   └── img/             # 画像ファイル
├── public/               # 本番用公開ファイル（圧縮版）
├── configs/              # システム設定
├── data/                 # データファイル
└── tests/                # テストファイル
```

### 4.2 モジュール内構造
```
modules/juchu/            # 受注モジュール例
├── controllers/          # コントローラ
│   ├── CustomerinfoController.php
│   ├── MitsuController.php
│   └── Juchu/           # カスタマイズ版コントローラ
│       ├── JuchuCustomerinfo.nowl.php
│       └── JuchuCustomerinfo.sano.php
└── views/
    └── smarty/
        ├── customerinfo/
        │   ├── input.tpl
        │   └── input-nowl.tpl  # カスタマイズ版
        └── mitsu/
            ├── input.tpl
            └── input-sano.tpl   # カスタマイズ版
```

---

## 5. カスタマイズキーシステム

### 5.1 概要
FDNの最大の特徴である **カスタマイズキーシステム** により、会社ごとの業務要件に対応したカスタマイズが可能です。

### 5.2 カスタマイズキーの仕組み
- 各会社に固有の **カスタマイズキー**（例：`nowl`, `sano`, `hakuzen`）を設定
- ファイル名に `.{カスタマイズキー}` を付けることで会社固有版を作成
- システムは優先順位に従ってファイルを読み込み

### 5.3 対象ファイル
| ファイル種別 | 命名例 | 説明 |
|-------------|--------|------|
| **PHP クラス** | `JuchuCustomerinfo.nowl.php` | ビジネスロジック |
| **コントローラ** | `CustomerinfoController.sano.php` | 画面制御 |
| **Smarty テンプレート** | `input-hakuzen.tpl` | 画面レイアウト |
| **JavaScript** | `juchu.customerinfo.nowl.js` | フロントエンド処理 |
| **CSS** | `juchu.customerinfo.sano.css` | スタイル |

### 5.4 読み込み優先順位
```
1. カスタマイズファイル（会社固有）
   例: JuchuCustomerinfo.nowl.php
2. 標準ファイル（共通）
   例: JuchuCustomerinfo.php
```

---

## 6. 主要モジュール

### 6.1 受注モジュール（juchu）
**役割**: 葬儀の受注から施行管理まで

**主要機能**:
- 顧客情報入力・管理
- 見積作成・管理
- 施行スケジュール管理
- 法事・法要管理

**主要コントローラ**:
- `CustomerinfoController`: 顧客情報管理
- `MitsuController`: 見積管理
- `HoujiController`: 法事管理

### 6.2 発注モジュール（hachu）
**役割**: 必要な商品・サービスの発注管理

**主要機能**:
- 発注伝票作成
- 仕入先管理
- 在庫管理連携

### 6.3 請求モジュール（seikyu）
**役割**: 料金計算・請求管理

**主要機能**:
- 請求書作成
- 入金管理
- 売掛金管理

### 6.4 参照モジュール（mref）
**役割**: 各種データの参照・検索

**主要機能**:
- 施行履歴検索
- 顧客検索
- 各種ダイアログ

### 6.5 マスタ管理モジュール（mstr）
**役割**: システムの基本データ管理

**主要機能**:
- 商品マスタ管理
- 会社情報管理
- ユーザー管理

---

## 7. データベース設計

### 7.1 データベース構成
- **システムDB**: `fdn_sys` - 会社情報、ユーザー情報
- **会社DB**: `fdndt{会社コード}` - 各会社の業務データ

### 7.2 主要テーブル

#### 施行関連
- `seko_kihon_info`: 施行基本情報
- `seko_nitei`: 施行日程情報
- `sekyu_saki_info`: 請求先情報

#### 売上・請求関連
- `uriage_denpyo`: 売上伝票
- `seikyu_zan`: 請求残高
- `nyukin_denpyo`: 入金伝票

#### マスタ関連
- `shohin_mst`: 商品マスタ
- `kaisya_mst`: 会社マスタ
- `s_user`: システムユーザー

### 7.3 命名規則
- **テーブル名**: スネークケース（例：`seko_kihon_info`）
- **カラム名**: スネークケース（例：`kaisya_cd`, `seko_no`）
- **削除フラグ**: `delete_flg`（0:有効, 1:削除）
- **タイムスタンプ**: `_cre_ts`（作成）, `_upd_ts`（更新）

### 7.4 データアクセス層
```php
// DataMapper パターン
$db = Msi_Sys_DbManager::getMyDb();
$mapper = new DataMapper_SekoKihonInfo($db);
$data = $mapper->find(['seko_no' => '12345']);

// ActiveRecord パターン
$user = DataMapper_SUser::findOne($db, ['login_cd' => 'user01']);
```

---

## 8. フロントエンド技術

### 8.1 JavaScript アーキテクチャ
```javascript
// Backbone.js MVC パターン
var AppModel = Backbone.Model.extend({
    defaults: {
        seko_no: null,
        customer_name: null
    },
    validation: {
        customer_name: {
            required: true,
            msg: '顧客名は必須です'
        }
    }
});

var AppView = Backbone.View.extend({
    el: '#my-form',
    events: {
        'click #save-btn': 'saveData'
    },
    bindings: {
        '#customer_name': 'customer_name'
    },
    saveData: function() {
        // Ajax でデータ保存
        $.ajax({
            url: $.msiJqlib.baseUrl() + '/juchu/customerinfo/save',
            type: 'POST',
            data: this.model.toJSON(),
            success: function(response) {
                $.msiJqlib.showInfo('保存しました');
            }
        });
    }
});
```

### 8.2 RequireJS モジュール管理
```javascript
// require.config.js
require.config({
    baseUrl: '/fdn/js',
    paths: {
        'jquery': 'vendor/jquery-1.10.2',
        'backbone': 'vendor/backbone',
        'underscore': 'vendor/underscore'
    }
});

// アプリケーション読み込み
define(['jquery', 'backbone', 'underscore'], function($, Backbone, _) {
    // アプリケーションロジック
});
```

### 8.3 Smarty テンプレート
```smarty
{* 顧客情報入力画面 *}
<form id="customer-form">
    <div class="form-group">
        <label>顧客名</label>
        <input type="text" id="customer_name" name="customer_name" 
               value="{$customer_name|escape}" class="form-control">
    </div>
    <button type="button" id="save-btn" class="btn btn-primary">保存</button>
</form>

{* JavaScript ファイル読み込み *}
{$js_added|smarty:nodefaults}
```

---

## 9. セキュリティ・認証

### 9.1 認証システム
- **Zend_Auth** ベースの認証機構
- **ロールベースアクセス制御**（RBAC）
- **セッション管理**

### 9.2 主要ロール
| ロール | 権限レベル | 説明 |
|--------|------------|------|
| `sysman` | 最高 | システム管理者 |
| `manager` | 高 | 管理者 |
| `tanto` | 中 | 担当者 |
| `jimu` | 中 | 事務 |
| `ro` | 低 | 読み取り専用 |

### 9.3 セキュリティ対策
```php
// SQLインジェクション対策
$stmt = $db->fetchPrepared(
    "SELECT * FROM seko_kihon_info WHERE seko_no = :seko_no",
    ['seko_no' => $sekoNo]
);

// XSS対策（Smarty）
{$user_input|escape}

// CSRF対策
App_LoginManagerAbst::securityCheckAddr();
```

---

## 10. 開発環境セットアップ

### 10.1 必要な環境
- **PHP 7.x**
- **PostgreSQL 9.x+**
- **Apache 2.x**
- **Composer**（依存関係管理）

### 10.2 設定ファイル
```ini
; configs/application.ini
[production]
phpSettings.display_startup_errors = 0
phpSettings.display_errors = 0
includePaths.library = APPLICATION_PATH "/../library"
bootstrap.path = APPLICATION_PATH "/Bootstrap.php"
bootstrap.class = "Bootstrap"
appnamespace = "Application"

; configs/multidbPg.ini
[production]
resources.multidb.sysdb.adapter = "Pdo_Pgsql"
resources.multidb.sysdb.host = "localhost"
resources.multidb.sysdb.dbname = "fdn_sys"
resources.multidb.sysdb.username = "fdndb"
resources.multidb.sysdb.password = "fdndb"
```

### 10.3 開発の流れ
1. **要件確認**: どの会社向けのカスタマイズか確認
2. **ファイル特定**: 既存の標準ファイルを確認
3. **カスタマイズファイル作成**: `.{カスタマイズキー}` 付きファイル作成
4. **テスト**: 該当会社の環境でテスト
5. **デプロイ**: 本番環境への反映

---

## 📚 参考資料

- [FDN基礎技術学習資料](./FDN基礎技術学習資料.md)
- [FDNダイアログ技術学習資料](./FDNダイアログ技術学習資料.md)
- [研修カリキュラム_FDN](./研修カリキュラム_FDN.md)
- [AIプロジェクトルール](./doc/AI/AIプロジェクトルール.md)

---

**作成日**: 2025年1月31日  
**対象**: 新規配属開発者  
**更新**: 必要に応じて随時更新
