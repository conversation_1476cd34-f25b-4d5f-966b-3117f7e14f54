CREATE TABLE public.s_kaisya (
    kaisya_cd                       CHAR(8)                                               NOT NULL,
    kaisya_lnm                      VARCHAR(60)                                           NOT NULL,
    kaisya_lknm                     VARCHAR(60)                                           ,
    kaisya_snm                      VARCHAR(20)                                           ,
    kaisya_sknm                     VARCHAR(20)                                           ,
    yubin_no                        VARCHAR(8)                                            ,
    addr1                           VARCHAR(60)                                           ,
    addr2                           VARCHAR(60)                                           ,
    addr1_kana                      VARCHAR(60)                                           ,
    addr2_kana                      VARCHAR(60)                                           ,
    tel                             VARCHAR(20)                                           ,
    fax                             VARCHAR(20)                                           ,
    daihyo_nm                       VARCHAR(40)                                           ,
    kesan_st_date                   DATE                                                  ,
    kesan_ed_date                   DATE                                                  ,
    raisensu_su                     NUMERIC(8,0)                    DEFAULT -1            NOT NULL,
    st_date                         DATE                                                  NOT NULL,
    ed_date                         DATE                            DEFAULT '2099/12/31'  NOT NULL,
    tanto_nm                        VARCHAR(40)                                           ,
    gojokai_db_kbn                  NUMERIC(2,0)                                          ,
    gojokai_server_nm               VARCHAR(256)                                          ,
    gojokai_port_id                 NUMERIC(4,0)                                          ,
    status                          BIGINT                          DEFAULT -1            NOT NULL,
    ip_addr_cond                    TEXT                                                  ,
    db_name                         VARCHAR(32)                                           ,
    db_info                         VARCHAR(256)                                          ,
    cstm_key                        VARCHAR(256)                                          ,
    license_info                    TEXT                                                  ,
    idle_timeout_sec                NUMERIC(8,0)                    DEFAULT -1            NOT NULL,
    is_databackup                   NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    databackup_info                 TEXT                                                  ,
    auth_type                       VARCHAR(32)                                           ,
    sa_or_cc                        NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    cert_check_info                 TEXT                                                  ,
    cert_info                       TEXT                                                  ,
    data_etc_1                      TEXT                                                  ,
    data_etc_2                      TEXT                                                  ,
    memo_1                          TEXT                                                  ,
    memo_2                          TEXT                                                  ,
    atta_file_1                     LO                                                    ,
    atta_file_1_info                TEXT                                                  ,
    atta_file_2                     LO                                                    ,
    atta_file_2_info                TEXT                                                  ,
    atta_file_3                     LO                                                    ,
    atta_file_3_info                TEXT                                                  ,
    atta_file_4                     LO                                                    ,
    atta_file_4_info                TEXT                                                  ,
    atta_file_5                     LO                                                    ,
    atta_file_5_info                TEXT                                                  ,
    no_del_flg                      BIGINT                          DEFAULT 0             NOT NULL,
    delete_flg                      NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    _req_id                         BIGINT                                                NOT NULL,
    _cre_user                       VARCHAR(64)                                           NOT NULL,
    _cre_ts                         TIMESTAMPTZ                     NOT NULL,
    _mod_user                       VARCHAR(64)                                           NOT NULL,
    _mod_ts                         TIMESTAMPTZ                                           NOT NULL,
    _mod_cnt                        BIGINT                          DEFAULT 0             NOT NULL,

    PRIMARY KEY ( kaisya_cd )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;


CREATE TRIGGER trg__atta_file_1 BEFORE UPDATE OR DELETE ON s_kaisya
 FOR EACH ROW EXECUTE PROCEDURE lo_manage(atta_file_1);


CREATE TRIGGER trg__atta_file_2 BEFORE UPDATE OR DELETE ON s_kaisya
 FOR EACH ROW EXECUTE PROCEDURE lo_manage(atta_file_2);


CREATE TRIGGER trg__atta_file_3 BEFORE UPDATE OR DELETE ON s_kaisya
 FOR EACH ROW EXECUTE PROCEDURE lo_manage(atta_file_3);


CREATE TRIGGER trg__atta_file_4 BEFORE UPDATE OR DELETE ON s_kaisya
 FOR EACH ROW EXECUTE PROCEDURE lo_manage(atta_file_4);


CREATE TRIGGER trg__atta_file_5 BEFORE UPDATE OR DELETE ON s_kaisya
 FOR EACH ROW EXECUTE PROCEDURE lo_manage(atta_file_5);


CREATE TRIGGER trg__stamp BEFORE INSERT OR UPDATE OR DELETE ON s_kaisya
    FOR EACH ROW EXECUTE PROCEDURE x_stamp_func();

COMMENT ON TABLE public.s_kaisya IS '会社管理';
COMMENT ON COLUMN public.s_kaisya.kaisya_cd IS '会社コード';
COMMENT ON COLUMN public.s_kaisya.kaisya_lnm IS '正式会社名';
COMMENT ON COLUMN public.s_kaisya.kaisya_lknm IS '正式カナ名';
COMMENT ON COLUMN public.s_kaisya.kaisya_snm IS '簡略会社名';
COMMENT ON COLUMN public.s_kaisya.kaisya_sknm IS '簡略カナ名';
COMMENT ON COLUMN public.s_kaisya.yubin_no IS '郵便番号';
COMMENT ON COLUMN public.s_kaisya.addr1 IS '住所１';
COMMENT ON COLUMN public.s_kaisya.addr2 IS '住所２';
COMMENT ON COLUMN public.s_kaisya.addr1_kana IS '住所１カナ';
COMMENT ON COLUMN public.s_kaisya.addr2_kana IS '住所２カナ';
COMMENT ON COLUMN public.s_kaisya.tel IS '電話番号';
COMMENT ON COLUMN public.s_kaisya.fax IS 'FAX番号';
COMMENT ON COLUMN public.s_kaisya.daihyo_nm IS '代表者名';
COMMENT ON COLUMN public.s_kaisya.kesan_st_date IS '決算開始日';
COMMENT ON COLUMN public.s_kaisya.kesan_ed_date IS '決算終了日';
COMMENT ON COLUMN public.s_kaisya.raisensu_su IS 'ライセンス数';
COMMENT ON COLUMN public.s_kaisya.st_date IS '利用開始日';
COMMENT ON COLUMN public.s_kaisya.ed_date IS '利用終了日';
COMMENT ON COLUMN public.s_kaisya.tanto_nm IS 'お客様担当者名';
COMMENT ON COLUMN public.s_kaisya.gojokai_db_kbn IS '互助会DB区分';
COMMENT ON COLUMN public.s_kaisya.gojokai_server_nm IS '互助会サーバ名';
COMMENT ON COLUMN public.s_kaisya.gojokai_port_id IS '互助会ポート名';
COMMENT ON COLUMN public.s_kaisya.status IS '状態';
COMMENT ON COLUMN public.s_kaisya.ip_addr_cond IS '発信IPアドレス制限情報';
COMMENT ON COLUMN public.s_kaisya.db_name IS 'DB名';
COMMENT ON COLUMN public.s_kaisya.db_info IS 'DB接続情報';
COMMENT ON COLUMN public.s_kaisya.cstm_key IS 'カスタマイズキー';
COMMENT ON COLUMN public.s_kaisya.license_info IS 'ライセンス情報';
COMMENT ON COLUMN public.s_kaisya.idle_timeout_sec IS '接続タイムアウト秒';
COMMENT ON COLUMN public.s_kaisya.is_databackup IS 'DBバックアップフラグ';
COMMENT ON COLUMN public.s_kaisya.databackup_info IS 'DBバックアップ情報';
COMMENT ON COLUMN public.s_kaisya.auth_type IS '認証タイプ';
COMMENT ON COLUMN public.s_kaisya.sa_or_cc IS '発信IP代替フラグ';
COMMENT ON COLUMN public.s_kaisya.cert_check_info IS '証明書チェック情報';
COMMENT ON COLUMN public.s_kaisya.cert_info IS '証明書情報';
COMMENT ON COLUMN public.s_kaisya.data_etc_1 IS '拡張情報１';
COMMENT ON COLUMN public.s_kaisya.data_etc_2 IS '拡張情報２';
COMMENT ON COLUMN public.s_kaisya.memo_1 IS 'メモ１';
COMMENT ON COLUMN public.s_kaisya.memo_2 IS 'メモ２';
COMMENT ON COLUMN public.s_kaisya.atta_file_1 IS '添付ファイル１';
COMMENT ON COLUMN public.s_kaisya.atta_file_1_info IS '添付ファイル１情報';
COMMENT ON COLUMN public.s_kaisya.atta_file_2 IS '添付ファイル２';
COMMENT ON COLUMN public.s_kaisya.atta_file_2_info IS '添付ファイル２情報';
COMMENT ON COLUMN public.s_kaisya.atta_file_3 IS '添付ファイル３';
COMMENT ON COLUMN public.s_kaisya.atta_file_3_info IS '添付ファイル３情報';
COMMENT ON COLUMN public.s_kaisya.atta_file_4 IS '添付ファイル４';
COMMENT ON COLUMN public.s_kaisya.atta_file_4_info IS '添付ファイル４情報';
COMMENT ON COLUMN public.s_kaisya.atta_file_5 IS '添付ファイル５';
COMMENT ON COLUMN public.s_kaisya.atta_file_5_info IS '添付ファイル５情報';
COMMENT ON COLUMN public.s_kaisya.no_del_flg IS '削除不可フラグ';
COMMENT ON COLUMN public.s_kaisya.delete_flg IS '削除フラグ';
COMMENT ON COLUMN public.s_kaisya._req_id IS '処理要求ID';
COMMENT ON COLUMN public.s_kaisya._cre_user IS '作成者';
COMMENT ON COLUMN public.s_kaisya._cre_ts IS '作成日時';
COMMENT ON COLUMN public.s_kaisya._mod_user IS '最終更新者';
COMMENT ON COLUMN public.s_kaisya._mod_ts IS '最終更新日時';
COMMENT ON COLUMN public.s_kaisya._mod_cnt IS '更新回数';

CREATE TABLE public.s_user (
    user_cd                         VARCHAR(32)                                           NOT NULL,
    kaisya_cd                       CHAR(8)                                               NOT NULL,
    login_cd                        VARCHAR(20)                                           NOT NULL,
    password                        VARCHAR(128)                                          ,
    st_date                         DATE                                                  NOT NULL,
    ed_date                         DATE                            DEFAULT '2099/12/31'  NOT NULL,
    user_lnm                        VARCHAR(50)                                           ,
    user_snm                        VARCHAR(10)                                           ,
    mail_addr                       VARCHAR(100)                                          ,
    sys_admin_flg                   BIGINT                          DEFAULT 0             NOT NULL,
    status                          BIGINT                          DEFAULT 0             NOT NULL,
    ip_addr_cond                    TEXT                                                  ,
    pw_chg_ts                       TIMESTAMPTZ                                           ,
    pw_archvies                     TEXT                                                  ,
    status_chg_ts                   TIMESTAMPTZ                                           ,
    account_type                    VARCHAR(32)                     DEFAULT 'norm'        ,
    no_del_flg                      BIGINT                          DEFAULT 0             NOT NULL,
    delete_flg                      NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    _req_id                         BIGINT                                                NOT NULL,
    _cre_user                       VARCHAR(64)                                           NOT NULL,
    _cre_ts                         TIMESTAMPTZ                     NOT NULL,
    _mod_user                       VARCHAR(64)                                           NOT NULL,
    _mod_ts                         TIMESTAMPTZ                                           NOT NULL,
    _mod_cnt                        BIGINT                          DEFAULT 0             NOT NULL,

    PRIMARY KEY ( user_cd )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;


CREATE TRIGGER trg__stamp BEFORE INSERT OR UPDATE OR DELETE ON s_user
    FOR EACH ROW EXECUTE PROCEDURE x_stamp_func();

COMMENT ON TABLE public.s_user IS '認証ユーザ管理';
COMMENT ON COLUMN public.s_user.user_cd IS '認証ユーザコード';
COMMENT ON COLUMN public.s_user.kaisya_cd IS '会社コード';
COMMENT ON COLUMN public.s_user.login_cd IS 'ログインコード';
COMMENT ON COLUMN public.s_user.password IS 'パスワード';
COMMENT ON COLUMN public.s_user.st_date IS '利用開始日';
COMMENT ON COLUMN public.s_user.ed_date IS '利用終了日';
COMMENT ON COLUMN public.s_user.user_lnm IS 'ユーザー名（標準）';
COMMENT ON COLUMN public.s_user.user_snm IS 'ユーザー名（略称）';
COMMENT ON COLUMN public.s_user.mail_addr IS 'メールアドレス';
COMMENT ON COLUMN public.s_user.sys_admin_flg IS 'システム管理者フラグ';
COMMENT ON COLUMN public.s_user.status IS '状態';
COMMENT ON COLUMN public.s_user.ip_addr_cond IS '発信IPアドレス制限情報';
COMMENT ON COLUMN public.s_user.pw_chg_ts IS 'パスワード変更日時';
COMMENT ON COLUMN public.s_user.pw_archvies IS 'パスワード変更記録';
COMMENT ON COLUMN public.s_user.status_chg_ts IS '状態変更日時';
COMMENT ON COLUMN public.s_user.account_type IS 'アカウント種別';
COMMENT ON COLUMN public.s_user.no_del_flg IS '削除不可フラグ';
COMMENT ON COLUMN public.s_user.delete_flg IS '削除フラグ';
COMMENT ON COLUMN public.s_user._req_id IS '処理要求ID';
COMMENT ON COLUMN public.s_user._cre_user IS '作成者';
COMMENT ON COLUMN public.s_user._cre_ts IS '作成日時';
COMMENT ON COLUMN public.s_user._mod_user IS '最終更新者';
COMMENT ON COLUMN public.s_user._mod_ts IS '最終更新日時';
COMMENT ON COLUMN public.s_user._mod_cnt IS '更新回数';

CREATE TABLE public.s_login_tbl (
    login_id                        BIGSERIAL                                             NOT NULL,
    kaisya_cd                       VARCHAR(32)                                           ,
    login_cd                        VARCHAR(32)                                           ,
    user_cd                         VARCHAR(32)                                           ,
    user_id                         BIGINT                                                ,
    status                          VARCHAR(20)                                           NOT NULL,
    sess_key                        VARCHAR(128)                                          ,
    login_ts                        VARCHAR(30)                                           ,
    access_ts                       VARCHAR(30)                                           ,
    access_cnt                      BIGINT                          DEFAULT 0             ,
    remote_addr                     VARCHAR(64)                                           ,
    browser_info                    TEXT                                                  ,

    PRIMARY KEY ( login_id )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;

COMMENT ON TABLE public.s_login_tbl IS 'ログイン管理';
COMMENT ON COLUMN public.s_login_tbl.login_id IS 'ログインID';
COMMENT ON COLUMN public.s_login_tbl.kaisya_cd IS '会社コード';
COMMENT ON COLUMN public.s_login_tbl.login_cd IS 'ログインCD';
COMMENT ON COLUMN public.s_login_tbl.user_cd IS 'ユーザーCD';
COMMENT ON COLUMN public.s_login_tbl.user_id IS 'ユーザーID';
COMMENT ON COLUMN public.s_login_tbl.status IS '状態';
COMMENT ON COLUMN public.s_login_tbl.sess_key IS 'セッションキー';
COMMENT ON COLUMN public.s_login_tbl.login_ts IS 'ログイン日時';
COMMENT ON COLUMN public.s_login_tbl.access_ts IS 'アクセス日時';
COMMENT ON COLUMN public.s_login_tbl.access_cnt IS 'アクセス数';
COMMENT ON COLUMN public.s_login_tbl.remote_addr IS 'IPアドレス';
COMMENT ON COLUMN public.s_login_tbl.browser_info IS 'ブラウザ情報';

CREATE TABLE public.s_log_auth (
    log_id                          BIGSERIAL                                             NOT NULL,
    log_ts                          VARCHAR(30)                                           NOT NULL,
    login_id                        BIGINT                                                ,
    kaisya_cd                       VARCHAR(32)                                           ,
    login_cd                        VARCHAR(32)                                           ,
    user_cd                         VARCHAR(32)                                           ,
    user_id                         BIGINT                                                ,
    event_type                      VARCHAR(20)                                           ,
    sess_key                        VARCHAR(128)                                          ,
    auth_type                       VARCHAR(10)                                           ,
    auth_level                      BIGINT                          DEFAULT 0             ,
    remote_addr                     VARCHAR(64)                                           ,
    browser_info                    TEXT                                                  ,
    message                         TEXT                                                  ,

    PRIMARY KEY ( log_id )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;

COMMENT ON TABLE public.s_log_auth IS '認証ログ';
COMMENT ON COLUMN public.s_log_auth.log_id IS 'ログID';
COMMENT ON COLUMN public.s_log_auth.log_ts IS 'ログ日時';
COMMENT ON COLUMN public.s_log_auth.login_id IS 'ログインID';
COMMENT ON COLUMN public.s_log_auth.kaisya_cd IS '会社コード';
COMMENT ON COLUMN public.s_log_auth.login_cd IS 'ログインCD';
COMMENT ON COLUMN public.s_log_auth.user_cd IS 'ユーザーCD';
COMMENT ON COLUMN public.s_log_auth.user_id IS 'ユーザーID';
COMMENT ON COLUMN public.s_log_auth.event_type IS 'イベントタイプ';
COMMENT ON COLUMN public.s_log_auth.sess_key IS 'セッションキー';
COMMENT ON COLUMN public.s_log_auth.auth_type IS '認証タイプ';
COMMENT ON COLUMN public.s_log_auth.auth_level IS '認証レベル';
COMMENT ON COLUMN public.s_log_auth.remote_addr IS 'IPアドレス';
COMMENT ON COLUMN public.s_log_auth.browser_info IS 'ブラウザ情報';
COMMENT ON COLUMN public.s_log_auth.message IS '内容';

CREATE TABLE public.s_log_std (
    log_id                          BIGSERIAL                                             NOT NULL,
    log_ts                          VARCHAR(30)                                           NOT NULL,
    priority                        BIGINT                                                NOT NULL,
    priority_name                   VARCHAR(10)                                           NOT NULL,
    login_id                        BIGINT                                                ,
    kaisya_cd                       VARCHAR(32)                                           ,
    login_cd                        VARCHAR(32)                                           ,
    user_cd                         VARCHAR(32)                                           ,
    user_id                         BIGINT                                                ,
    server_id                       VARCHAR(20)                                           ,
    process_id                      BIGINT                                                ,
    request_id                      BIGINT                                                ,
    module_name                     VARCHAR(64)                                           ,
    location                        VARCHAR(256)                                          ,
    message                         TEXT                                                  ,

    PRIMARY KEY ( log_id )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;

COMMENT ON TABLE public.s_log_std IS '標準ログ';
COMMENT ON COLUMN public.s_log_std.log_id IS 'ログID';
COMMENT ON COLUMN public.s_log_std.log_ts IS 'ログ日時';
COMMENT ON COLUMN public.s_log_std.priority IS 'ログレベル';
COMMENT ON COLUMN public.s_log_std.priority_name IS 'ログレベル名';
COMMENT ON COLUMN public.s_log_std.login_id IS 'ログインID';
COMMENT ON COLUMN public.s_log_std.kaisya_cd IS '会社コード';
COMMENT ON COLUMN public.s_log_std.login_cd IS 'ログインCD';
COMMENT ON COLUMN public.s_log_std.user_cd IS 'ユーザーCD';
COMMENT ON COLUMN public.s_log_std.user_id IS 'ユーザーID';
COMMENT ON COLUMN public.s_log_std.server_id IS 'サーバID';
COMMENT ON COLUMN public.s_log_std.process_id IS 'プロセスID';
COMMENT ON COLUMN public.s_log_std.request_id IS '要求ID';
COMMENT ON COLUMN public.s_log_std.module_name IS 'モジュール名';
COMMENT ON COLUMN public.s_log_std.location IS '出力箇所（カテゴリ';
COMMENT ON COLUMN public.s_log_std.message IS '内容';

CREATE TABLE public.s_sys_info (
    sys_info_id                     BIGSERIAL                                             NOT NULL,
    st_date                         DATE                                                  NOT NULL,
    ed_date                         DATE                                                  NOT NULL,
    ken_nm                          VARCHAR(100)                                          ,
    naiyo                           TEXT                                                  ,
    level                           VARCHAR(10)                                           NOT NULL,
    delete_flg                      NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    _req_id                         BIGINT                                                NOT NULL,
    _cre_user                       VARCHAR(64)                                           NOT NULL,
    _cre_ts                         TIMESTAMPTZ                     NOT NULL,
    _mod_user                       VARCHAR(64)                                           NOT NULL,
    _mod_ts                         TIMESTAMPTZ                                           NOT NULL,
    _mod_cnt                        BIGINT                          DEFAULT 0             NOT NULL,

    PRIMARY KEY ( sys_info_id )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;


CREATE TRIGGER trg__stamp BEFORE INSERT OR UPDATE OR DELETE ON s_sys_info
    FOR EACH ROW EXECUTE PROCEDURE x_stamp_func();

COMMENT ON TABLE public.s_sys_info IS 'システム連絡情報';
COMMENT ON COLUMN public.s_sys_info.sys_info_id IS 'システム連絡情報ID';
COMMENT ON COLUMN public.s_sys_info.st_date IS '掲載開始日';
COMMENT ON COLUMN public.s_sys_info.ed_date IS '掲載終了日';
COMMENT ON COLUMN public.s_sys_info.ken_nm IS '件名';
COMMENT ON COLUMN public.s_sys_info.naiyo IS '内容';
COMMENT ON COLUMN public.s_sys_info.level IS 'レベル';
COMMENT ON COLUMN public.s_sys_info.delete_flg IS '削除フラグ';
COMMENT ON COLUMN public.s_sys_info._req_id IS '処理要求ID';
COMMENT ON COLUMN public.s_sys_info._cre_user IS '作成者';
COMMENT ON COLUMN public.s_sys_info._cre_ts IS '作成日時';
COMMENT ON COLUMN public.s_sys_info._mod_user IS '最終更新者';
COMMENT ON COLUMN public.s_sys_info._mod_ts IS '最終更新日時';
COMMENT ON COLUMN public.s_sys_info._mod_cnt IS '更新回数';

CREATE TABLE public.s_ot_user (
    user_nonce                      VARCHAR(128)                                          NOT NULL,
    user_cd                         VARCHAR(32)                                           NOT NULL,
    kaisya_cd                       CHAR(8)                                               NOT NULL,
    login_cd                        VARCHAR(20)                                           NOT NULL,
    password                        VARCHAR(128)                                          ,
    issue_ts                        TIMESTAMPTZ                     DEFAULT now()         NOT NULL,
    expire_ts                       TIMESTAMPTZ                                           ,
    proc_max_cnt                    BIGINT                          DEFAULT 1             ,
    proc_cnt                        BIGINT                          DEFAULT 0             NOT NULL,
    st_date                         DATE                            DEFAULT '2000/01/01'  NOT NULL,
    ed_date                         DATE                            DEFAULT '2099/12/31'  NOT NULL,
    user_lnm                        VARCHAR(50)                                           ,
    user_snm                        VARCHAR(10)                                           ,
    mail_addr                       VARCHAR(100)                                          ,
    sys_admin_flg                   BIGINT                          DEFAULT 0             NOT NULL,
    status                          BIGINT                          DEFAULT 0             NOT NULL,
    ip_addr_cond                    TEXT                                                  ,
    pw_chg_ts                       TIMESTAMPTZ                                           ,
    pw_archvies                     TEXT                                                  ,
    status_chg_ts                   TIMESTAMPTZ                                           ,
    account_type                    VARCHAR(32)                     DEFAULT 'ot'          ,
    no_del_flg                      BIGINT                          DEFAULT 0             NOT NULL,
    delete_flg                      NUMERIC(1,0)                    DEFAULT 0             NOT NULL,
    _req_id                         BIGINT                                                NOT NULL,
    _cre_user                       VARCHAR(64)                                           NOT NULL,
    _cre_ts                         TIMESTAMPTZ                     NOT NULL,
    _mod_user                       VARCHAR(64)                                           NOT NULL,
    _mod_ts                         TIMESTAMPTZ                                           NOT NULL,
    _mod_cnt                        BIGINT                          DEFAULT 0             NOT NULL,

    PRIMARY KEY ( user_nonce )
            USING INDEX TABLESPACE pg_default
) TABLESPACE pg_default;;


CREATE TRIGGER trg__stamp BEFORE INSERT OR UPDATE OR DELETE ON s_ot_user
    FOR EACH ROW EXECUTE PROCEDURE x_stamp_func();

COMMENT ON TABLE public.s_ot_user IS 'ワンタイム・ユーザ管理';
COMMENT ON COLUMN public.s_ot_user.user_nonce IS '認証ユーザノンス';
COMMENT ON COLUMN public.s_ot_user.user_cd IS '認証ユーザコード';
COMMENT ON COLUMN public.s_ot_user.kaisya_cd IS '会社コード';
COMMENT ON COLUMN public.s_ot_user.login_cd IS 'ログインコード';
COMMENT ON COLUMN public.s_ot_user.password IS 'パスワード';
COMMENT ON COLUMN public.s_ot_user.issue_ts IS '発行日時';
COMMENT ON COLUMN public.s_ot_user.expire_ts IS '有効期限';
COMMENT ON COLUMN public.s_ot_user.proc_max_cnt IS '実行可能回数';
COMMENT ON COLUMN public.s_ot_user.proc_cnt IS '実行回数';
COMMENT ON COLUMN public.s_ot_user.st_date IS '利用開始日';
COMMENT ON COLUMN public.s_ot_user.ed_date IS '利用終了日';
COMMENT ON COLUMN public.s_ot_user.user_lnm IS 'ユーザー名（標準）';
COMMENT ON COLUMN public.s_ot_user.user_snm IS 'ユーザー名（略称）';
COMMENT ON COLUMN public.s_ot_user.mail_addr IS 'メールアドレス';
COMMENT ON COLUMN public.s_ot_user.sys_admin_flg IS 'システム管理者フラグ';
COMMENT ON COLUMN public.s_ot_user.status IS '状態';
COMMENT ON COLUMN public.s_ot_user.ip_addr_cond IS '発信IPアドレス制限情報';
COMMENT ON COLUMN public.s_ot_user.pw_chg_ts IS 'パスワード変更日時';
COMMENT ON COLUMN public.s_ot_user.pw_archvies IS 'パスワード変更記録';
COMMENT ON COLUMN public.s_ot_user.status_chg_ts IS '状態変更日時';
COMMENT ON COLUMN public.s_ot_user.account_type IS 'アカウント種別';
COMMENT ON COLUMN public.s_ot_user.no_del_flg IS '削除不可フラグ';
COMMENT ON COLUMN public.s_ot_user.delete_flg IS '削除フラグ';
COMMENT ON COLUMN public.s_ot_user._req_id IS '処理要求ID';
COMMENT ON COLUMN public.s_ot_user._cre_user IS '作成者';
COMMENT ON COLUMN public.s_ot_user._cre_ts IS '作成日時';
COMMENT ON COLUMN public.s_ot_user._mod_user IS '最終更新者';
COMMENT ON COLUMN public.s_ot_user._mod_ts IS '最終更新日時';
COMMENT ON COLUMN public.s_ot_user._mod_cnt IS '更新回数';

